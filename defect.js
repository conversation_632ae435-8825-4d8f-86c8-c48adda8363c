// ==UserScript==
// @name         defect default
// @namespace    http://tampermonkey.net/
// @version      2025-07-24
// @description  缺陷单默认填充
// <AUTHOR>
// @match        http://defect.hikvision.com.cn/approve/document?flowInstId=*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=hikvision.com.cn
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 是否指派设置为 false
    const assignNo = document.querySelector('#assignNo');
    window.changeAssign(assignNo);

    // 结论设置为 true
    const conclusionYes = document.querySelector('#solved');
    window.changeConclusion(conclusionYes, '', 'usertask3');

    // 测试漏测设置为 false
    window.escapeConclusion('2','usertask3');

    // 问题引入设置为 新引入（缺陷修复）
    const newIntroduceBug = document.querySelector('#newIntroduceBug');
    window.changeEscape(newIntroduceBug);

    // 引入活动设置为 编码
    const introducedActivity = document.querySelector('#introducedActivity');
    introducedActivity.value = '编码';

    // 引入原因设置为 其他
    const reason = document.querySelector('#reason');
    reason.value = '其他';

    // 设置所属模块为第一个选项
    const modules = document.querySelector('.latest-module-container').childNodes
    if (modules.length) {
      modules[0].click();
    }

    // 设置解决方法为 产生原因：；解决手段：；
    const solution = document.querySelector('#solution');
    solution.value = '产生原因：；解决手段：；';

    // 设置修改清单
    const modifiedList = document.querySelector('#modifiedList > tbody').childNodes;
    if(modifiedList.length) {
      modifiedList.forEach((item,index) => {
        item.querySelector(`#achivementType${index}`).value = '其他';
        item.querySelector(`#fileLibrary${index}`).value = 'GIT';
        item.querySelector(`#storagePath${index}`).value = 'https://sys-gitlab.hikvision.com.cn/IPSC/Integration/DeviceWeb/deviceweb5.0';
        item.querySelector(`#version${index}`).value = 'HEAD';
      })
    }
})();
